apt-get install gcc make libxml2-dev autoconf ca-certificates unzip nodejs curl libcurl4-openssl-dev pkg-config 

mkdir /home/<USER>
cd /home/<USER>
wget http://be2.php.net/get/php-5.5.15.tar.bz2/from/this/mirror -O php-5.5.15.tar.bz2
tar -xjvf php-5.5.15.tar.bz2
cd php-5.5.15
./configure --prefix /usr/local --with-mysql --enable-maintainer-zts --enable-sockets --with-openssl --with-pdo-mysql 
make
make install
cd /home/<USER>
wget http://pecl.php.net/get/pthreads-2.0.7.tgz
tar -xvzf pthreads-2.0.7.tgz
cd pthreads-2.0.7
/usr/local/bin/phpize
./configure
make
make install
echo 'date.timezone = Europe/Paris' >> /usr/local/lib/php.ini
echo 'extension=pthreads.so' >> /usr/local/lib/php.ini


#### opt #####
apt-get install mysql-server
#and make database
#############


mkdir /home/<USER>
cd /home/<USER>
wget https://github.com/deStrO/eBot-CSGO/archive/master.zip
unzip master.zip
mv eBot-CSGO-master ebot-csgo
cd ebot-csgo
curl --silent --location https://deb.nodesource.com/setup_0.12 | bash -
apt-get install -y nodejs
npm install socket.io@0.9.12 archiver formidable
curl -sS https://getcomposer.org/installer | php5
php5 composer.phar install
# edit config config/config.ini with IP/PORT and MySQL access
cd /home/<USER>
wget https://github.com/deStrO/eBot-CSGO-Web/archive/master.zip
unzip master.zip
mv eBot-CSGO-Web-master ebot-web
cd ebot-web
cp config/app_user.yml.default config/app_user.yml
# edit config config/app_user.yml with ebot_ip and ebot_port
# edit database config/database.yml
mkdir cache
php5 symfony cc
php5 symfony doctrine:build --all --no-confirmation
php5 symfony guard:create-user --is-super-admin admin@ebot admin admin

#To start ebot daemon
/home/<USER>/ebot-csgo
php bootstrap.php
