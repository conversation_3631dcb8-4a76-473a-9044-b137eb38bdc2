<?php

/**
 * eBot - A bot for match management for CS:GO
 * @license     http://creativecommons.org/licenses/by/3.0/ Creative Commons 3.0
 * <AUTHOR> <<EMAIL>>
 * @version     3.0
 * @date        21/10/2012
 */

namespace eBot\Message\CSGO;

use eBot\Message\Message;
use eBot\Message\Type\RoundStart as MessageObject;

class RoundStart extends Message {

    public function __construct() {
        parent::__construct('/^World triggered "Round_Start"/');
    }

    public function process() {
        $o = new MessageObject();
        $o->setTime(time());

        return $o;
    }

}

?>
