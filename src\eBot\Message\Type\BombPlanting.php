<?php

/**
 * eBot - A bot for match management for CS:GO
 * @license     http://creativecommons.org/licenses/by/3.0/ Creative Commons 3.0
 * <AUTHOR> <<EMAIL>>
 * @version     3.0
 * @date        21/10/2012
 */

namespace eBot\Message\Type;

use eBot\Message\Type;

class BombPlanting extends Type {

    public $userId = "";
    public $userName = "";
    public $userTeam = "";
    public $userSteamid = "";

    public function __construct() {
        $this->setName("BombPlanting");
    }

}

?>
