; eBot - A bot for match management for CS:GO
; @license     http://creativecommons.org/licenses/by/3.0/ Creative Commons 3.0
; <AUTHOR> <<EMAIL>>
; @version     3.0
; @date        21/10/2012

[BDD]
MYSQL_IP = "127.0.0.1"
MYSQL_PORT = "3306"
MYSQL_USER = "ebotv3"
MYSQL_PASS = "ebotv3"
MYSQL_BASE = "ebotv3"

[Config]
BOT_IP = "127.0.0.1"
BOT_PORT = 12360
SSL_ENABLED = false
SSL_CERTIFICATE_PATH = "ssl/cert.pem"
SSL_KEY_PATH = "ssl/key.pem"
EXTERNAL_LOG_IP = "" ; use this in case your server isn't binded with the external IP (behind a NAT)
MANAGE_PLAYER = 1
DELAY_BUSY_SERVER = 120
NB_MAX_MATCHS = 0
PAUSE_METHOD = "nextRound" ; nextRound or instantConfirm or instantNoConfirm
NODE_STARTUP_METHOD = "node" ; binary file name or none in case you are starting it with forever or manually
LOG_ADDRESS_SERVER = "http://***********:12345"
WEBSOCKET_SECRET_KEY = "generatestrongsecretkey"

[Redis]
REDIS_HOST = "127.0.0.1"
REDIS_PORT = 6379
REDIS_AUTH_USERNAME =
REDIS_AUTH_PASSWORD =
REDIS_CHANNEL_LOG = "ebot-logs"
REDIS_CHANNEL_EBOT_FROM_WS = "ebot-from-ws"
REDIS_CHANNEL_EBOT_TO_WS = "ebot-to-ws"

[Match]
LO3_METHOD = "restart" ; restart or csay or esl
KO3_METHOD = "restart" ; restart or csay or esl
DEMO_DOWNLOAD = true ; true or false :: whether gotv demos will be downloaded from the gameserver after matchend or not
REMIND_RECORD = false ; true will print the 3x "Remember to record your own POV demos if needed!" messages, false will not
DAMAGE_REPORT = true ; true will print damage reports at end of round to players, false will not
USE_DELAY_END_RECORD = true ; use the tv_delay to record postpone the tv_stoprecord & upload
TIMEOUT_ENABLED = false
TIMEOUT_USE_MATCH_CONFIG = false ; If false, eBot will sends the RCON commands for the timeout
TIMEOUT_TIME = 120 ; mp_team_timeout_time
TIMEOUT_PER_TEAM_PER_MATCH = 1 ; mp_team_timeout_max
TIMEOUT_OT_ADD_EACH = 0 ; mp_team_timeout_ot_add_each
TIMEOUT_OT_ADD_ONCE = 0 ; mp_team_timeout_ot_add_once
TIMEOUT_OT_MAX = 1 ; mp_team_timeout_ot_max

[MAPS]
MAP[] = "de_mirage"
MAP[] = "de_dust2"
MAP[] = "de_inferno"
MAP[] = "de_overpass"
MAP[] = "de_nuke"
MAP[] = "de_vertigo"
MAP[] = "de_ancient"
MAP[] = "de_anubis"

[WORKSHOP IDs]

[Settings]
COMMAND_STOP_DISABLED = true
RECORD_METHOD = "matchstart" ; matchstart or knifestart
DELAY_READY = true
