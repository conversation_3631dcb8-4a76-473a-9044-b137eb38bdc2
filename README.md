## Before going further

If you want some help about eBot, you can join our Discord : https://discord.gg/mcEwFhpWpD

eBot-CS2
==============

The eBot is a full managed server-bot written in PHP and nodeJS. eBot features easy match creation and tons of player and matchstats. Once it's setup, using the eBot is simple and fast.

## Requirements
* Linux, Windows
* PHP 7.4 or newer
* NodeJS (LTS min)

The following extensions are required:
* `MySQL`
* `Redis`
* `JSON`


See installation guide for a step-by-step install instruction.

## License
The code is under Creative Commons license. You can find all details here: http://creativecommons.org/licenses/by/3.0/

You can copy, distribute, modify the source code, but you have to keep the license terms.

## Credits
* Julien 'deStrO' <PERSON>rdons (<EMAIL>)

## Thanks
* <PERSON><PERSON> (RegnaM)
* Ph3nol
* Fabian 'Basert' Gruber

## See also
* [eSport-tools.net website](http://www.esport-tools.net/)

