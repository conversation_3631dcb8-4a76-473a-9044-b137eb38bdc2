; eBot - A bot for match management for CS:GO
; @license     http://creativecommons.org/licenses/by/3.0/ Creative Commons 3.0
; <AUTHOR> <<EMAIL>>
; @version     3.0
; @date        21/10/2012

;[\eBot\Plugins\Official\PluginMatchScoreNotifier]
;url=http://elan.esport-tools.net/api/update/{MATCH_ID}

;[\eBot\Plugins\Official\ToornamentNotifier]
;url=http://your.ebot.url/matchs/toornament/export/{MATCH_ID}
;key=changeme

;[\eBot\Plugins\Official\DiscordIntegration]
;url[]=https://discord.com/api/webhooks/...
;scopes[]=score,match_status,error,kills,ebot
