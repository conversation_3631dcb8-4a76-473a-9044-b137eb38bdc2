; eBot - A bot for match management for CS:GO
; @license     http://creativecommons.org/licenses/by/3.0/ Creative Commons 3.0
; <AUTHOR> <<EMAIL>>
; @version     3.0
; @date        21/10/2012

; Don't touch to this file, configuration for messages
[CSGO]
message[] = "\eBot\Message\CSGO\Attacked"
message[] = "\eBot\Message\CSGO\BombDefusing"
message[] = "\eBot\Message\CSGO\BombPlanting"
message[] = "\eBot\Message\CSGO\ChangeMap"
message[] = "\eBot\Message\CSGO\ChangeName"
message[] = "\eBot\Message\CSGO\Connected"
message[] = "\eBot\Message\CSGO\Disconnected"
message[] = "\eBot\Message\CSGO\EnteredTheGame"
message[] = "\eBot\Message\CSGO\GotTheBomb"
message[] = "\eBot\Message\CSGO\JoinTeam"
message[] = "\eBot\Message\CSGO\Kill"
message[] = "\eBot\Message\CSGO\KillAssist"
message[] = "\eBot\Message\CSGO\RoundEnd"
message[] = "\eBot\Message\CSGO\RoundRestart"
message[] = "\eBot\Message\CSGO\RoundScored"
message[] = "\eBot\Message\CSGO\RemindRoundScored"
message[] = "\eBot\Message\CSGO\RoundStart"
message[] = "\eBot\Message\CSGO\RoundSpawn"
message[] = "\eBot\Message\CSGO\Purchased"
message[] = "\eBot\Message\CSGO\Say"
message[] = "\eBot\Message\CSGO\SayTeam"
message[] = "\eBot\Message\CSGO\SwitchTeam"
message[] = "\eBot\Message\CSGO\TeamScored"
message[] = "\eBot\Message\CSGO\ThrewStuff"
message[] = "\eBot\Message\CSGO\Pause"
